# Configurações de Autenticação Google
# Copie este arquivo para .env e configure suas credenciais

# Google OAuth Client ID
# Obtenha em: https://console.cloud.google.com/
EXPO_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id_here.apps.googleusercontent.com

# OpenAI API Key
# Obtenha em: https://platform.openai.com/api-keys
EXPO_PUBLIC_OPENAI_API_KEY=sk-your_openai_api_key_here

# Configurações opcionais da OpenAI
EXPO_PUBLIC_OPENAI_MODEL=gpt-4o-mini
EXPO_PUBLIC_OPENAI_MAX_TOKENS=2000

# Instruções para configurar Google OAuth:
# 1. Acesse https://console.cloud.google.com/
# 2. Crie um projeto ou selecione um existente
# 3. Ative a Google+ API
# 4. Vá para "Credenciais" > "Criar credenciais" > "ID do cliente OAuth 2.0"
# 5. Selecione "Aplicativo da Web"
# 6. Configure as origens autorizadas:
#    - http://localhost:19006 (desenvolvimento)
# 7. Configure os URIs de redirecionamento:
#    - http://localhost:19006/auth (desenvolvimento)
# 8. Copie o Client ID e cole acima

# Instruções para configurar OpenAI:
# 1. Acesse https://platform.openai.com/
# 2. Faça login ou crie uma conta
# 3. Vá para "API Keys" no menu lateral
# 4. Clique em "Create new secret key"
# 5. Copie a chave e cole acima
# 6. Configure billing em https://platform.openai.com/account/billing
