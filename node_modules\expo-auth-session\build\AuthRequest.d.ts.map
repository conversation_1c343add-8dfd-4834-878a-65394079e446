{"version": 3, "file": "AuthRequest.d.ts", "sourceRoot": "", "sources": ["../src/AuthRequest.ts"], "names": [], "mappings": "AAIA,OAAO,EACL,iBAAiB,EACjB,wBAAwB,EACxB,mBAAmB,EACnB,YAAY,EACZ,MAAM,EACN,qBAAqB,EACtB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AASxD;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,qBAAa,WAAY,YAAW,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC;IAClE;;OAEG;IACI,KAAK,EAAE,MAAM,CAAC;IACd,GAAG,EAAE,MAAM,GAAG,IAAI,CAAQ;IAC1B,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,aAAa,CAAC,EAAE,MAAM,CAAC;IAE9B,QAAQ,CAAC,YAAY,EAAE,YAAY,GAAG,MAAM,CAAC;IAC7C,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC1B,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC7C,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;IAC3B,QAAQ,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IAClD,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;IAC7B,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B,QAAQ,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC;IAC/B,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;gBAExB,OAAO,EAAE,iBAAiB;IA2CtC;;OAEG;IACG,yBAAyB,IAAI,OAAO,CAAC,iBAAiB,CAAC;IAoB7D;;;;;OAKG;IACG,WAAW,CACf,SAAS,EAAE,qBAAqB,EAChC,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,GAAE,wBAA6B,GACjD,OAAO,CAAC,iBAAiB,CAAC;IAuD7B,cAAc,CAAC,GAAG,EAAE,MAAM,GAAG,iBAAiB;IAgC9C;;;;OAIG;IACG,gBAAgB,CAAC,SAAS,EAAE,qBAAqB,GAAG,OAAO,CAAC,MAAM,CAAC;YA8C3D,sBAAsB;CAWrC"}