{"version": 3, "file": "Provider.types.js", "sourceRoot": "", "sources": ["../../src/providers/Provider.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { AuthRequestConfig } from '../AuthRequest.types';\n\nexport type ProviderAuthRequestConfig = AuthRequestConfig & {\n  /**\n   * Language for the sign in UI, in the form of ISO 639-1 language code optionally followed by a dash\n   * and ISO 3166-1 alpha-2 region code, such as 'it' or 'pt-PT'.\n   * Only set this value if it's different from the system default (which you can access via expo-localization).\n   */\n  language?: string;\n};\n"]}