import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    <PERSON><PERSON>,
    FlatList,
    RefreshControl,
    StyleSheet,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { DocumentCard } from '@/components/ui/DocumentCard';
import { Theme } from '@/constants/Colors';
import { useAuthStore } from '@/store/authStore';
import { useDocumentsStore } from '@/store/documentsStore';
import { router } from 'expo-router';

export default function DocumentsScreen() {
  const { documents, removeDocument, analyzeDocument, isAnalyzing } = useDocumentsStore();
  const { user } = useAuthStore();
  const [refreshing, setRefreshing] = React.useState(false);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // TODO: Implementar refresh dos documentos
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleUpload = () => {
    router.push('/(tabs)/upload');
  };

  const handleDocumentPress = (documentId: string) => {
    router.push(`/document/${documentId}`);
  };

  const handleAnalyze = async (documentId: string) => {
    try {
      if (!user || user.credits <= 0) {
        Alert.alert(
          'Créditos Insuficientes',
          'Você não possui créditos suficientes para realizar uma análise. Adquira mais créditos para continuar.',
          [
            { text: 'OK' },
            { text: 'Comprar Créditos', onPress: () => router.push('/(tabs)/profile') }
          ]
        );
        return;
      }

      // Mostrar opções de análise
      Alert.alert(
        'Tipo de Análise',
        'Escolha o tipo de análise que deseja realizar:',
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Resumo Geral',
            onPress: () => performAnalysis(documentId, 'document_summary')
          },
          {
            text: 'Análise de Contrato',
            onPress: () => performAnalysis(documentId, 'contract_analysis')
          },
          {
            text: 'Avaliação de Riscos',
            onPress: () => performAnalysis(documentId, 'risk_assessment')
          },
        ]
      );
    } catch (error) {
      Alert.alert('Erro', 'Não foi possível iniciar a análise');
    }
  };

  const performAnalysis = async (documentId: string, analysisType: 'document_summary' | 'contract_analysis' | 'risk_assessment') => {
    try {
      await analyzeDocument(documentId, analysisType);
      Alert.alert(
        'Análise Concluída',
        'A análise do documento foi realizada com sucesso!',
        [
          { text: 'OK' },
          { text: 'Ver Resultado', onPress: () => handleDocumentPress(documentId) }
        ]
      );
    } catch (error: any) {
      Alert.alert('Erro na Análise', error.message || 'Não foi possível analisar o documento');
    }
  };

  const renderDocument = ({ item }: { item: any }) => (
    <DocumentCard
      document={item}
      onPress={() => handleDocumentPress(item.id)}
      onDelete={() => removeDocument(item.id)}
      onAnalyze={() => handleAnalyze(item.id)}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="document-text-outline" size={64} color="#9CA3AF" />
      <ThemedText type="subtitle" style={styles.emptyTitle}>
        Nenhum documento ainda
      </ThemedText>
      <ThemedText type="caption" style={styles.emptyDescription}>
        Faça upload do seu primeiro documento para começar a análise com IA
      </ThemedText>
      <Button
        title="Fazer Upload"
        onPress={handleUpload}
        style={styles.emptyButton}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.header}>
        <View>
          <ThemedText type="title">Meus Documentos</ThemedText>
          <ThemedText type="caption" style={styles.subtitle}>
            {user?.credits} créditos disponíveis
          </ThemedText>
        </View>
        <Button
          title="Upload"
          onPress={handleUpload}
          size="small"
          icon={<Ionicons name="add" size={16} color="#FFFFFF" style={{ marginRight: 4 }} />}
        />
      </ThemedView>

      <FlatList
        data={documents}
        renderItem={renderDocument}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
      />

      {/* Loading overlay para análise */}
      {isAnalyzing && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#6366F1" />
            <ThemedText style={styles.loadingText}>
              Analisando documento...
            </ThemedText>
            <ThemedText type="caption" style={styles.loadingSubtext}>
              Isso pode levar alguns segundos
            </ThemedText>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  subtitle: {
    opacity: 0.7,
    marginTop: 2,
  },
  listContent: {
    padding: Theme.spacing.lg,
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.xl,
  },
  emptyTitle: {
    marginTop: Theme.spacing.lg,
    marginBottom: Theme.spacing.sm,
    textAlign: 'center',
  },
  emptyDescription: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: Theme.spacing.xl,
    lineHeight: 20,
  },
  emptyButton: {
    minWidth: 150,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingContainer: {
    backgroundColor: '#FFFFFF',
    padding: Theme.spacing.xl,
    borderRadius: Theme.borderRadius.lg,
    alignItems: 'center',
    minWidth: 200,
    ...Theme.shadows.lg,
  },
  loadingText: {
    marginTop: Theme.spacing.md,
    textAlign: 'center',
    fontWeight: '600',
  },
  loadingSubtext: {
    marginTop: Theme.spacing.xs,
    textAlign: 'center',
    opacity: 0.7,
  },
});
