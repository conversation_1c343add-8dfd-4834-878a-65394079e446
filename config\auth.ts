/**
 * Configurações de autenticação
 *
 * Para configurar a autenticação com Google:
 *
 * 1. Acesse o Google Cloud Console: https://console.cloud.google.com/
 * 2. Crie um novo projeto ou selecione um existente
 * 3. Ative a Google+ API
 * 4. Vá para "Credenciais" > "Criar credenciais" > "ID do cliente OAuth 2.0"
 * 5. Configure as origens autorizadas:
 *    - Para desenvolvimento: http://localhost:19006
 *    - Para produção: sua URL de produção
 * 6. Configure os URIs de redirecionamento:
 *    - Para desenvolvimento: http://localhost:19006/auth
 *    - Para produção: sua URL de produção + /auth
 * 7. Copie o Client ID e cole abaixo
 */

export const AUTH_CONFIG = {
  // Substitua pelo seu Google Client ID
  GOOGLE_CLIENT_ID: process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID || 'YOUR_GOOGLE_CLIENT_ID_HERE',

  // Configurações do OAuth
  SCOPES: ['openid', 'profile', 'email'],

  // URLs de redirecionamento
  REDIRECT_URI: {
    development: 'http://localhost:19006/auth',
    production: 'https://your-app.com/auth', // Substitua pela sua URL
  },

  // Endpoints do Google
  ENDPOINTS: {
    authorization: 'https://accounts.google.com/o/oauth2/v2/auth',
    token: 'https://oauth2.googleapis.com/token',
    userInfo: 'https://www.googleapis.com/oauth2/v2/userinfo',
  },
};

export const OPENAI_CONFIG = {
  // Chave da API da OpenAI
  API_KEY: process.env.EXPO_PUBLIC_OPENAI_API_KEY || 'YOUR_OPENAI_API_KEY_HERE',

  // Modelo padrão
  MODEL: process.env.EXPO_PUBLIC_OPENAI_MODEL || 'gpt-4o-mini',

  // Configurações padrão
  MAX_TOKENS: parseInt(process.env.EXPO_PUBLIC_OPENAI_MAX_TOKENS || '2000'),
  TEMPERATURE: 0.7,

  // Endpoints da OpenAI
  ENDPOINTS: {
    chat: 'https://api.openai.com/v1/chat/completions',
    transcription: 'https://api.openai.com/v1/audio/transcriptions',
    vision: 'https://api.openai.com/v1/chat/completions', // Para análise de imagens
  },

  // Prompts padrão para diferentes tipos de análise
  PROMPTS: {
    document_summary: `Analise o documento fornecido e forneça:
1. Um resumo conciso do conteúdo principal
2. Os pontos-chave mais importantes
3. Qualquer informação crítica que se destaque
4. Recomendações ou próximos passos, se aplicável

Responda em português brasileiro de forma clara e objetiva.`,

    contract_analysis: `Analise este contrato e identifique:
1. Partes envolvidas e suas responsabilidades
2. Termos e condições principais
3. Prazos e datas importantes
4. Cláusulas de risco ou que merecem atenção especial
5. Recomendações para revisão ou negociação

Responda em português brasileiro com foco em aspectos jurídicos relevantes.`,

    risk_assessment: `Avalie os riscos presentes neste documento:
1. Identifique potenciais riscos financeiros, legais ou operacionais
2. Classifique os riscos por nível de severidade (baixo, médio, alto)
3. Sugira medidas de mitigação para cada risco identificado
4. Destaque cláusulas ou termos que precisam de atenção especial

Responda em português brasileiro com análise detalhada de riscos.`,

    custom: `Analise o documento conforme solicitado pelo usuário. Forneça uma resposta detalhada, precisa e útil em português brasileiro.`
  }
};

/**
 * Instruções para configurar as variáveis de ambiente:
 *
 * 1. Crie um arquivo .env na raiz do projeto
 * 2. Adicione a linha: EXPO_PUBLIC_GOOGLE_CLIENT_ID=seu_client_id_aqui
 * 3. Reinicie o servidor de desenvolvimento
 *
 * Exemplo de .env:
 * EXPO_PUBLIC_GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
 */
