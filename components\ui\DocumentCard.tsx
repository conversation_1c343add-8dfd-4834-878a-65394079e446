import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Theme } from '@/constants/Colors';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Document } from '@/store/documentsStore';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    Alert,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

interface DocumentCardProps {
  document: Document;
  onPress?: () => void;
  onDelete?: () => void;
  onAnalyze?: () => void;
}

export function DocumentCard({
  document,
  onPress,
  onDelete,
  onAnalyze,
}: DocumentCardProps) {
  const textSecondaryColor = useThemeColor({}, 'textSecondary');
  const primaryColor = useThemeColor({}, 'primary');
  const errorColor = useThemeColor({}, 'error');
  const successColor = useThemeColor({}, 'success');

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return 'document-text';
      case 'image':
        return 'image';
      case 'audio':
        return 'musical-notes';
      case 'doc':
        return 'document';
      default:
        return 'document';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleDelete = () => {
    Alert.alert(
      'Excluir Documento',
      'Tem certeza que deseja excluir este documento? Esta ação não pode ser desfeita.',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: onDelete,
        },
      ]
    );
  };

  return (
    <ThemedView type="card" style={styles.container}>
      <TouchableOpacity
        style={styles.content}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <View style={styles.header}>
          <View style={styles.fileInfo}>
            <Ionicons
              name={getFileIcon(document.type) as any}
              size={24}
              color={primaryColor}
              style={styles.fileIcon}
            />
            <View style={styles.fileDetails}>
              <ThemedText style={styles.fileName} numberOfLines={1}>
                {document.name}
              </ThemedText>
              <ThemedText style={styles.fileSize} type="caption">
                {formatFileSize(document.size)} • {formatDate(document.uploadedAt)}
              </ThemedText>
            </View>
          </View>

          <View style={styles.actions}>
            {document.analysis && (
              <View style={[styles.badge, { backgroundColor: successColor }]}>
                <ThemedText style={styles.badgeText} type="caption">
                  Analisado
                </ThemedText>
              </View>
            )}
          </View>
        </View>

        {document.analysis && (
          <View style={styles.analysisPreview}>
            <ThemedText style={styles.analysisTitle} type="caption" weight="medium">
              Resumo da Análise:
            </ThemedText>
            <ThemedText style={styles.analysisText} numberOfLines={2}>
              {document.analysis.summary}
            </ThemedText>
          </View>
        )}
      </TouchableOpacity>

      <View style={styles.footer}>
        {!document.analysis && onAnalyze && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: primaryColor }]}
            onPress={onAnalyze}
          >
            <Ionicons name="analytics" size={16} color="#FFFFFF" />
            <ThemedText style={styles.actionButtonText}>
              Analisar
            </ThemedText>
          </TouchableOpacity>
        )}

        {document.analysis && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: successColor }]}
            onPress={onPress}
          >
            <Ionicons name="eye" size={16} color="#FFFFFF" />
            <ThemedText style={styles.actionButtonText}>
              Ver Análise
            </ThemedText>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton, { backgroundColor: errorColor }]}
          onPress={handleDelete}
        >
          <Ionicons name="trash" size={16} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: Theme.spacing.md,
    borderRadius: Theme.borderRadius.lg,
    ...Theme.shadows.md,
  },
  content: {
    padding: Theme.spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  fileInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  fileIcon: {
    marginRight: Theme.spacing.sm,
    marginTop: 2,
  },
  fileDetails: {
    flex: 1,
  },
  fileName: {
    fontWeight: Theme.fontWeight.medium,
    marginBottom: 2,
  },
  fileSize: {
    opacity: 0.7,
  },
  actions: {
    marginLeft: Theme.spacing.sm,
  },
  badge: {
    paddingHorizontal: Theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: Theme.borderRadius.sm,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
  },
  analysisPreview: {
    marginTop: Theme.spacing.md,
    paddingTop: Theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  analysisTitle: {
    marginBottom: Theme.spacing.xs,
    opacity: 0.8,
  },
  analysisText: {
    lineHeight: 18,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: Theme.spacing.md,
    paddingBottom: Theme.spacing.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.md,
    paddingVertical: Theme.spacing.sm,
    borderRadius: Theme.borderRadius.md,
  },
  deleteButton: {
    paddingHorizontal: Theme.spacing.sm,
  },
  actionButtonText: {
    color: '#FFFFFF',
    marginLeft: Theme.spacing.xs,
    fontSize: Theme.fontSize.sm,
    fontWeight: Theme.fontWeight.medium,
  },
});
