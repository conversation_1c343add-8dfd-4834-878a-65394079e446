import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { useAuthStore } from '@/store/authStore';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Theme } from '@/constants/Colors';

export default function ProfileScreen() {
  const { user, logout } = useAuthStore();
  const borderColor = useThemeColor({}, 'border');
  const primaryColor = useThemeColor({}, 'primary');

  const handleLogout = () => {
    Alert.alert(
      'Sair da Conta',
      'Tem certeza que deseja sair?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Sair', 
          style: 'destructive',
          onPress: logout,
        },
      ]
    );
  };

  const ProfileItem = ({ 
    icon, 
    title, 
    value, 
    onPress 
  }: {
    icon: string;
    title: string;
    value?: string;
    onPress?: () => void;
  }) => (
    <ThemedView style={[styles.profileItem, { borderBottomColor: borderColor }]}>
      <View style={styles.profileItemLeft}>
        <Ionicons name={icon as any} size={24} color={primaryColor} />
        <View style={styles.profileItemText}>
          <ThemedText type="subtitle" style={styles.profileItemTitle}>
            {title}
          </ThemedText>
          {value && (
            <ThemedText type="caption" style={styles.profileItemValue}>
              {value}
            </ThemedText>
          )}
        </View>
      </View>
      {onPress && (
        <Ionicons name="chevron-forward" size={20} color={borderColor} />
      )}
    </ThemedView>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <ThemedView style={styles.header}>
          <ThemedText type="title">Perfil</ThemedText>
        </ThemedView>

        <ThemedView style={styles.userCard}>
          <View style={styles.userInfo}>
            {user?.avatar ? (
              <Image source={{ uri: user.avatar }} style={styles.avatar} />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: primaryColor }]}>
                <Ionicons name="person" size={32} color="#FFFFFF" />
              </View>
            )}
            
            <View style={styles.userDetails}>
              <ThemedText type="subtitle" style={styles.userName}>
                {user?.name || 'Usuário'}
              </ThemedText>
              <ThemedText type="caption" style={styles.userEmail}>
                {user?.email || '<EMAIL>'}
              </ThemedText>
            </View>
          </View>

          <View style={styles.creditsContainer}>
            <View style={[styles.creditsCard, { backgroundColor: primaryColor + '20' }]}>
              <Ionicons name="diamond" size={24} color={primaryColor} />
              <View style={styles.creditsInfo}>
                <ThemedText type="caption" style={styles.creditsLabel}>
                  Créditos Disponíveis
                </ThemedText>
                <ThemedText type="subtitle" weight="bold" style={styles.creditsValue}>
                  {user?.credits || 0}
                </ThemedText>
              </View>
            </View>
          </View>
        </ThemedView>

        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Conta
          </ThemedText>
          
          <ProfileItem
            icon="person-circle"
            title="Informações Pessoais"
            value="Editar perfil"
            onPress={() => {}}
          />
          
          <ProfileItem
            icon="card"
            title="Planos e Pagamentos"
            value="Gerenciar assinatura"
            onPress={() => {}}
          />
          
          <ProfileItem
            icon="shield-checkmark"
            title="Privacidade e Segurança"
            value="Configurações de privacidade"
            onPress={() => {}}
          />
        </View>

        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Suporte
          </ThemedText>
          
          <ProfileItem
            icon="help-circle"
            title="Central de Ajuda"
            value="FAQ e tutoriais"
            onPress={() => {}}
          />
          
          <ProfileItem
            icon="mail"
            title="Contato"
            value="Fale conosco"
            onPress={() => {}}
          />
          
          <ProfileItem
            icon="star"
            title="Avaliar App"
            value="Deixe sua avaliação"
            onPress={() => {}}
          />
        </View>

        <View style={styles.section}>
          <Button
            title="Sair da Conta"
            onPress={handleLogout}
            variant="outline"
            style={styles.logoutButton}
            icon={<Ionicons name="log-out" size={20} color="#EF4444" />}
            textStyle={{ color: '#EF4444' }}
          />
        </View>

        <View style={styles.footer}>
          <ThemedText type="caption" style={styles.version}>
            DocuAI Analyzer v1.0.0
          </ThemedText>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: Theme.spacing.lg,
  },
  header: {
    marginBottom: Theme.spacing.lg,
  },
  userCard: {
    padding: Theme.spacing.lg,
    borderRadius: Theme.borderRadius.lg,
    marginBottom: Theme.spacing.lg,
    ...Theme.shadows.md,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Theme.spacing.lg,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  avatarPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userDetails: {
    marginLeft: Theme.spacing.md,
    flex: 1,
  },
  userName: {
    marginBottom: 2,
  },
  userEmail: {
    opacity: 0.7,
  },
  creditsContainer: {
    marginTop: Theme.spacing.md,
  },
  creditsCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Theme.spacing.md,
    borderRadius: Theme.borderRadius.md,
  },
  creditsInfo: {
    marginLeft: Theme.spacing.sm,
  },
  creditsLabel: {
    opacity: 0.8,
  },
  creditsValue: {
    marginTop: 2,
  },
  section: {
    marginBottom: Theme.spacing.xl,
  },
  sectionTitle: {
    marginBottom: Theme.spacing.md,
    opacity: 0.8,
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Theme.spacing.md,
    borderBottomWidth: 1,
  },
  profileItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profileItemText: {
    marginLeft: Theme.spacing.md,
    flex: 1,
  },
  profileItemTitle: {
    marginBottom: 2,
  },
  profileItemValue: {
    opacity: 0.6,
  },
  logoutButton: {
    borderColor: '#EF4444',
  },
  footer: {
    alignItems: 'center',
    marginTop: Theme.spacing.xl,
    paddingTop: Theme.spacing.lg,
  },
  version: {
    opacity: 0.5,
  },
});
