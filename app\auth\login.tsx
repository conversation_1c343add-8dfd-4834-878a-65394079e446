import { router } from 'expo-router';
import React from 'react';
import {
    <PERSON><PERSON>,
    ScrollView,
    StyleSheet,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { Theme } from '@/constants/Colors';
import { useAuthStore } from '@/store/authStore';

export default function LoginScreen() {
  const { signInWithGoogle, isLoading } = useAuthStore();

  const handleGoogleSignIn = async () => {
    try {
      await signInWithGoogle();
      router.replace('/(tabs)');
    } catch (error) {
      Alert.alert(
        'Erro na Autenticação',
        'Não foi possível fazer login com Google. Tente novamente.'
      );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <ThemedView style={styles.content}>
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Ionicons name="document-text" size={64} color="#6366F1" />
            </View>
            <ThemedText type="title" style={styles.title}>
              DocuAI Analyzer
            </ThemedText>
            <ThemedText type="caption" style={styles.subtitle}>
              Analise seus documentos com inteligência artificial
            </ThemedText>
          </View>

          <View style={styles.authSection}>
            <Button
              title="Continuar com Google"
              onPress={handleGoogleSignIn}
              loading={isLoading}
              style={styles.googleButton}
              icon={
                <Ionicons
                  name="logo-google"
                  size={20}
                  color="#FFFFFF"
                  style={{ marginRight: 8 }}
                />
              }
            />

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <ThemedText type="caption" style={styles.dividerText}>
                Acesso seguro e rápido
              </ThemedText>
              <View style={styles.dividerLine} />
            </View>
          </View>

          <View style={styles.features}>
            <View style={styles.feature}>
              <Ionicons name="shield-checkmark" size={24} color="#10B981" />
              <ThemedText type="caption" style={styles.featureText}>
                Dados protegidos e criptografados
              </ThemedText>
            </View>

            <View style={styles.feature}>
              <Ionicons name="flash" size={24} color="#F59E0B" />
              <ThemedText type="caption" style={styles.featureText}>
                Análise rápida com IA avançada
              </ThemedText>
            </View>

            <View style={styles.feature}>
              <Ionicons name="cloud-upload" size={24} color="#6366F1" />
              <ThemedText type="caption" style={styles.featureText}>
                Suporte a múltiplos formatos
              </ThemedText>
            </View>
          </View>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.xxl,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: Theme.spacing.xxl,
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Theme.spacing.lg,
  },
  title: {
    textAlign: 'center',
    marginBottom: Theme.spacing.sm,
    color: '#6366F1',
  },
  subtitle: {
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  authSection: {
    marginBottom: Theme.spacing.xxl,
  },
  googleButton: {
    backgroundColor: '#4285F4',
    marginBottom: Theme.spacing.lg,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: Theme.spacing.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  dividerText: {
    marginHorizontal: Theme.spacing.md,
    opacity: 0.6,
  },
  features: {
    gap: Theme.spacing.lg,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Theme.spacing.sm,
  },
  featureText: {
    marginLeft: Theme.spacing.md,
    flex: 1,
    lineHeight: 18,
  },
});
