import { openaiService } from '@/services/openaiService';
import { create } from 'zustand';
import { useAuthStore } from './authStore';

export interface Document {
  id: string;
  name: string;
  type: 'pdf' | 'image' | 'audio' | 'doc';
  size: number;
  uri: string;
  uploadedAt: string;
  category?: string;
  analysis?: DocumentAnalysis;
}

export interface DocumentAnalysis {
  id: string;
  documentId: string;
  summary: string;
  keyPoints: string[];
  risks?: string[];
  recommendations?: string[];
  extractedData: Record<string, any>;
  confidence: number;
  analyzedAt: string;
  prompt?: string;
}

interface DocumentsState {
  documents: Document[];
  analyses: DocumentAnalysis[];
  isUploading: boolean;
  isAnalyzing: boolean;
  uploadProgress: number;

  // Actions
  addDocument: (document: Document) => void;
  removeDocument: (documentId: string) => void;
  updateDocument: (documentId: string, updates: Partial<Document>) => void;
  addAnalysis: (analysis: DocumentAnalysis) => void;
  analyzeDocument: (documentId: string, analysisType: 'document_summary' | 'contract_analysis' | 'risk_assessment' | 'custom', customPrompt?: string) => Promise<void>;
  setUploading: (uploading: boolean) => void;
  setAnalyzing: (analyzing: boolean) => void;
  setUploadProgress: (progress: number) => void;
  clearDocuments: () => void;
}

export const useDocumentsStore = create<DocumentsState>((set, get) => ({
  documents: [],
  analyses: [],
  isUploading: false,
  isAnalyzing: false,
  uploadProgress: 0,

  addDocument: (document: Document) => {
    set((state) => ({
      documents: [...state.documents, document],
    }));
  },

  removeDocument: (documentId: string) => {
    set((state) => ({
      documents: state.documents.filter(doc => doc.id !== documentId),
      analyses: state.analyses.filter(analysis => analysis.documentId !== documentId),
    }));
  },

  updateDocument: (documentId: string, updates: Partial<Document>) => {
    set((state) => ({
      documents: state.documents.map(doc =>
        doc.id === documentId ? { ...doc, ...updates } : doc
      ),
    }));
  },

  addAnalysis: (analysis: DocumentAnalysis) => {
    set((state) => ({
      analyses: [...state.analyses, analysis],
    }));

    // Atualizar o documento com a análise
    const { updateDocument } = get();
    updateDocument(analysis.documentId, { analysis });
  },

  analyzeDocument: async (documentId: string, analysisType: 'document_summary' | 'contract_analysis' | 'risk_assessment' | 'custom', customPrompt?: string) => {
    const { documents, addAnalysis, setAnalyzing } = get();
    const document = documents.find(doc => doc.id === documentId);

    if (!document) {
      throw new Error('Documento não encontrado');
    }

    // Verificar se o usuário tem créditos
    const authStore = useAuthStore.getState();
    if (!authStore.user || authStore.user.credits <= 0) {
      throw new Error('Créditos insuficientes para análise');
    }

    setAnalyzing(true);

    try {
      let analysisResult;

      if (document.type === 'image') {
        // Análise de imagem
        analysisResult = await openaiService.analyzeImage({
          documentId,
          content: '',
          type: analysisType,
          customPrompt,
          documentType: 'image'
        }, document.uri);
      } else if (document.type === 'audio') {
        // Primeiro transcrever o áudio
        const transcription = await openaiService.transcribeAudio(document.uri);

        if (!transcription.success || !transcription.text) {
          throw new Error('Falha na transcrição do áudio');
        }

        // Depois analisar o texto transcrito
        analysisResult = await openaiService.analyzeText({
          documentId,
          content: transcription.text,
          type: analysisType,
          customPrompt,
          documentType: 'audio'
        });
      } else {
        // Para documentos de texto (PDF, DOC, etc.)
        // TODO: Implementar extração de texto de PDFs
        // Por enquanto, usar um placeholder
        const textContent = 'Conteúdo do documento extraído aqui...';

        analysisResult = await openaiService.analyzeText({
          documentId,
          content: textContent,
          type: analysisType,
          customPrompt,
          documentType: 'text'
        });
      }

      if (!analysisResult.success || !analysisResult.analysis) {
        throw new Error(analysisResult.error || 'Falha na análise');
      }

      // Criar objeto de análise
      const analysis: DocumentAnalysis = {
        id: Date.now().toString(),
        documentId,
        summary: analysisResult.analysis.summary,
        keyPoints: analysisResult.analysis.keyPoints,
        risks: analysisResult.analysis.risks,
        recommendations: analysisResult.analysis.recommendations,
        extractedData: analysisResult.analysis.extractedData,
        confidence: analysisResult.analysis.confidence,
        analyzedAt: new Date().toISOString(),
        prompt: customPrompt
      };

      // Adicionar análise
      addAnalysis(analysis);

      // Descontar crédito do usuário
      authStore.updateUser({
        credits: authStore.user.credits - 1
      });

    } catch (error) {
      console.error('Erro na análise:', error);
      throw error;
    } finally {
      setAnalyzing(false);
    }
  },

  setUploading: (uploading: boolean) => {
    set({ isUploading: uploading });
  },

  setAnalyzing: (analyzing: boolean) => {
    set({ isAnalyzing: analyzing });
  },

  setUploadProgress: (progress: number) => {
    set({ uploadProgress: progress });
  },

  clearDocuments: () => {
    set({
      documents: [],
      analyses: [],
    });
  },
}));
