{"version": 3, "file": "Video.js", "sourceRoot": "", "sources": ["../src/Video.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,cAAc,EAAE,KAAK,EAAiB,UAAU,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAEtF,OAAO,EACL,0BAA0B,EAC1B,+CAA+C,EAC/C,yBAAyB,EACzB,iBAAiB,EAEjB,aAAa,GAMd,MAAM,MAAM,CAAC;AACd,OAAO,gBAAgB,MAAM,oBAAoB,CAAC;AAClD,OAAO,UAAU,MAAM,cAAc,CAAC;AACtC,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAML,UAAU,GAEX,MAAM,eAAe,CAAC;AAEvB,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC;IAChC,IAAI,EAAE;QACJ,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,UAAU;KAC1B;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,UAAU;QACpB,IAAI,EAAE,CAAC;QACP,GAAG,EAAE,CAAC;QACN,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,CAAC;QACT,UAAU,EAAE,SAAS;KACtB;IACD,KAAK,EAAE;QACL,QAAQ,EAAE,UAAU;QACpB,IAAI,EAAE,CAAC;QACP,GAAG,EAAE,CAAC;QACN,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,CAAC;KACV;CACF,CAAC,CAAC;AAEH,IAAI,4BAA4B,GAAY,KAAK,CAAC;AAElD,kGAAkG;AAClG,qEAAqE;AACrE,MAAM,yBAAyB,GAAG,gBAAgB,CAAC;AACnD,MAAM,oBAAoB,GAAG,gBAAgB,CAAC;AAE9C,MAAM,KAAM,SAAQ,KAAK,CAAC,SAAiC;IACzD,UAAU,GAAG,KAAK,CAAC,SAAS,EAAwD,CAAC;IACrF,uBAAuB,GAAgD,IAAI,CAAC;IAE5E,YAAY,KAAiB;QAC3B,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,KAAK,GAAG;YACX,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAA6B;QAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QAC5C,IAAI,CAAC,WAAW;YAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5E,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;IAED,mBAAmB;IAEnB,gBAAgB,GAAG,CAAC,MAAwB,EAAE,EAAE;QAC9C,IACE,IAAI,CAAC,KAAK,CAAC,UAAU;YACrB,MAAM,CAAC,QAAQ;YACf,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC,EACjD,CAAC;YACD,IAAI,CAAC,QAAQ,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;YACtC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC;IACH,CAAC,CAAC;IAEF,qCAAqC,GAAG,KAAK,EAC3C,SAAqD,EAC1B,EAAE;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACtC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;QAC9F,CAAC;QAED,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAC;QACxD,MAAM,MAAM,GAAqB,MAAM,SAAS,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC9B,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF,oBAAoB;IAEpB,cAAc,GAAG,KAAK,EAAE,KAAc,EAA6B,EAAE;QACnE,OAAO,IAAI,CAAC,qCAAqC,CAAC,CAAC,GAAW,EAAE,EAAE,CAChE,oBAAoB,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAC/C,CAAC;IACJ,CAAC,CAAC;IAEF;;;;;OAKG;IACH,uBAAuB,GAAG,KAAK,IAA+B,EAAE;QAC9D,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC,CAAC;IAEF;;;;OAIG;IACH,uBAAuB,GAAG,KAAK,IAA+B,EAAE;QAC9D,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC,CAAC;IAEF,0DAA0D;IAC1D,wEAAwE;IAExE;;OAEG;IACH,cAAc,GAAG,KAAK,IAA+B,EAAE;QACrD,OAAO,IAAI,CAAC,qCAAqC,CAAC,CAAC,GAAW,EAAE,EAAE,CAChE,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAClC,CAAC;IACJ,CAAC,CAAC;IAEF;;OAEG;IACH,SAAS,GAAG,KAAK,EACf,MAAwB,EACxB,gBAAuC,EAAE,EACzC,gBAAyB,IAAI,EACF,EAAE;QAC7B,MAAM,EAAE,YAAY,EAAE,iBAAiB,EAAE,GACvC,MAAM,+CAA+C,CAAC,MAAM,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAC9F,OAAO,IAAI,CAAC,qCAAqC,CAAC,CAAC,GAAW,EAAE,EAAE,CAChE,UAAU,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAC9D,CAAC;IACJ,CAAC,CAAC;IAEF;;;OAGG;IACH,WAAW,GAAG,KAAK,IAA+B,EAAE;QAClD,OAAO,IAAI,CAAC,qCAAqC,CAAC,CAAC,GAAW,EAAE,EAAE,CAChE,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,CAC/B,CAAC;IACJ,CAAC,CAAC;IAEF,oBAAoB;QAClB,wDAAwD;QACxD,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE;YAC5B,gGAAgG;YAChG,4BAA4B;YAC5B,6EAA6E;YAC7E,oCAAoC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,cAAc,GAAG,KAAK,EAAE,MAA6B,EAA6B,EAAE;QAClF,0BAA0B,CAAC,MAAM,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC,qCAAqC,CAAC,CAAC,GAAW,EAAE,EAAE,CAChE,UAAU,CAAC,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,CAC1C,CAAC;IACJ,CAAC,CAAC;IAEF;;OAEG;IACH,WAAW,GAAG,KAAK,EAAE,SAAgC,EAAE,EAA6B,EAAE;QACpF,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,IAAI,CAAC,qCAAqC,CAAC,CAAC,GAAW,EAAE,EAAE,CAChE,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE;YAC1B,GAAG,MAAM;YACT,cAAc,EAAE,CAAC;YACjB,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;IACJ,CAAC,CAAC;IAEF;;;;;;;;;;;OAWG;IACH,yBAAyB,CAAC,sBAAmE;QAC3F,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED,mEAAmE;IACnE,SAAS,CAAmC;IAC5C,qBAAqB,CAGU;IAC/B,UAAU,CAAmC;IAC7C,SAAS,CAAmC;IAC5C,gBAAgB,CAGe;IAC/B,YAAY,CAImB;IAC/B,cAAc,CAAoE;IAClF,eAAe,CAAmD;IAClE,iBAAiB,CAAqD;IACtE,8BAA8B,CAEC;IAE/B,oBAAoB;IAEpB,6BAA6B,GAAG,CAAC,KAAwC,EAAE,EAAE;QAC3E,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IAC3C,CAAC,CAAC;IAEF,gDAAgD;IAChD,kBAAkB,GAAG,GAAG,EAAE;QACxB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC,CAAC;IAEF,aAAa,GAAG,CAAC,KAAwC,EAAE,EAAE;QAC3D,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IAC3C,CAAC,CAAC;IAEF,cAAc,GAAG,CAAC,KAAyC,EAAE,EAAE;QAC7D,MAAM,KAAK,GAAW,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC;QAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC;IAEF,wBAAwB,GAAG,CAAC,KAAiD,EAAE,EAAE;QAC/E,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,CAAC;IAEF,yBAAyB,GAAG,CAAC,KAAkD,EAAE,EAAE;QACjF,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACnD,CAAC;IACH,CAAC,CAAC;IAEF,aAAa,GAAG,GAAG,EAAE;QACnB,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC;QAE5D,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CACrD,CAAC,eAAe,CACd,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAChD,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAa,CAAC,EACjC,CACH,CAAC,CAAC,CAAC,IAAI,CAAC;IACX,CAAC,CAAC;IAEF,MAAM;QACJ,8BAA8B,EAAE,CAAC;QAEjC,MAAM,MAAM,GAAG,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;QAEzE,IAAI,gBAAgB,GAAG,yBAAyB,CAAC,SAAS,CAAC;QAC3D,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACzC,IAAI,UAAU,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC;gBACtC,gBAAgB,GAAG,yBAAyB,CAAC,WAAW,CAAC;YAC3D,CAAC;iBAAM,IAAI,UAAU,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC;gBAC7C,gBAAgB,GAAG,yBAAyB,CAAC,cAAc,CAAC;YAC9D,CAAC;iBAAM,IAAI,UAAU,KAAK,UAAU,CAAC,KAAK,EAAE,CAAC;gBAC3C,gBAAgB,GAAG,yBAAyB,CAAC,eAAe,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,MAAM,MAAM,GAA0B,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAE7D;YACE,8BAA8B;YAC9B,gBAAgB;YAChB,YAAY;YACZ,MAAM;YACN,oBAAoB;YACpB,QAAQ;YACR,SAAS;YACT,WAAW;SAEd,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACjB,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAQ,CAAC;YACzC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,gCAAgC;QAChC,MAAM,WAAW,GAAqB;YACpC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;gBAClB,QAAQ;gBACR,wBAAwB;gBACxB,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;aACvB,CAAC;YACF,KAAK,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YACvC,UAAU,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YAClD,MAAM;YACN,UAAU,EAAE,gBAAgB;YAC5B,MAAM;YACN,cAAc,EAAE,IAAI,CAAC,6BAA6B;YAClD,WAAW,EAAE,IAAI,CAAC,kBAAkB;YACpC,MAAM,EAAE,IAAI,CAAC,aAAa;YAC1B,OAAO,EAAE,IAAI,CAAC,cAAc;YAC5B,iBAAiB,EAAE,IAAI,CAAC,wBAAwB;YAChD,kBAAkB,EAAE,IAAI,CAAC,yBAAyB;SACnD,CAAC;QAEF,OAAO,CACL,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAC7B;QAAA,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,EACpF;QAAA,CAAC,IAAI,CAAC,aAAa,EAAE,CACvB;MAAA,EAAE,IAAI,CAAC,CACR,CAAC;IACJ,CAAC;CACF;AAED,SAAS,IAAI,CAAC,KAA0B,EAAE,SAAmB;IAC3D,MAAM,MAAM,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;IAC5B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QACjC,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,8BAA8B;IACrC,IAAI,OAAO,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAC7C,4BAA4B,GAAG,IAAI,CAAC;QACpC,OAAO,CAAC,GAAG,CACT,+FAA+F;YAC7F,sGAAsG,CACzG,CAAC;IACJ,CAAC;AACH,CAAC;AAED,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AAE9C,0FAA0F;AAC1F,eAAe,KAAK,CAAC", "sourcesContent": ["import * as React from 'react';\nimport { findNodeHandle, Image, NativeMethods, StyleSheet, View } from 'react-native';\n\nimport {\n  assertStatusValuesInBounds,\n  getNativeSourceAndFullInitialStatusForLoadAsync,\n  getNativeSourceFromSource,\n  getUnloadedStatus,\n  Playback,\n  PlaybackMixin,\n  AVPlaybackSource,\n  AVPlaybackStatus,\n  AVPlaybackStatusToSet,\n  AVPlaybackTolerance,\n  PitchCorrectionQuality,\n} from './AV';\nimport ExpoVideoManager from './ExpoVideoManager';\nimport ExponentAV from './ExponentAV';\nimport ExponentVideo from './ExponentVideo';\nimport {\n  ExponentVideoComponent,\n  VideoFullscreenUpdateEvent,\n  VideoNativeProps,\n  VideoProps,\n  VideoReadyForDisplayEvent,\n  ResizeMode,\n  VideoState,\n} from './Video.types';\n\nconst _STYLES = StyleSheet.create({\n  base: {\n    overflow: 'hidden',\n    pointerEvents: 'box-none',\n  },\n  poster: {\n    position: 'absolute',\n    left: 0,\n    top: 0,\n    right: 0,\n    bottom: 0,\n    resizeMode: 'contain',\n  },\n  video: {\n    position: 'absolute',\n    left: 0,\n    top: 0,\n    right: 0,\n    bottom: 0,\n  },\n});\n\nlet didWarnAboutVideoDeprecation: boolean = false;\n\n// On a real device UIManager should be present, however when running offline tests with jest-expo\n// we have to use the provided native module mock to access constants\nconst ExpoVideoManagerConstants = ExpoVideoManager;\nconst ExpoVideoViewManager = ExpoVideoManager;\n\nclass Video extends React.Component<VideoProps, VideoState> implements Playback {\n  _nativeRef = React.createRef<InstanceType<ExponentVideoComponent> & NativeMethods>();\n  _onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null;\n\n  constructor(props: VideoProps) {\n    super(props);\n    this.state = {\n      showPoster: !!props.usePoster,\n    };\n  }\n\n  /**\n   * @hidden\n   */\n  setNativeProps(nativeProps: VideoNativeProps) {\n    const nativeVideo = this._nativeRef.current;\n    if (!nativeVideo) throw new Error(`native video reference is not defined.`);\n    nativeVideo.setNativeProps(nativeProps);\n  }\n\n  // Internal methods\n\n  _handleNewStatus = (status: AVPlaybackStatus) => {\n    if (\n      this.state.showPoster &&\n      status.isLoaded &&\n      (status.isPlaying || status.positionMillis !== 0)\n    ) {\n      this.setState({ showPoster: false });\n    }\n\n    if (this.props.onPlaybackStatusUpdate) {\n      this.props.onPlaybackStatusUpdate(status);\n    }\n    if (this._onPlaybackStatusUpdate) {\n      this._onPlaybackStatusUpdate(status);\n    }\n  };\n\n  _performOperationAndHandleStatusAsync = async (\n    operation: (tag: number) => Promise<AVPlaybackStatus>\n  ): Promise<AVPlaybackStatus> => {\n    const video = this._nativeRef.current;\n    if (!video) {\n      throw new Error(`Cannot complete operation because the Video component has not yet loaded`);\n    }\n\n    const handle = findNodeHandle(this._nativeRef.current)!;\n    const status: AVPlaybackStatus = await operation(handle);\n    this._handleNewStatus(status);\n    return status;\n  };\n\n  // Fullscreening API\n\n  _setFullscreen = async (value: boolean): Promise<AVPlaybackStatus> => {\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExpoVideoViewManager.setFullscreen(tag, value)\n    );\n  };\n\n  /**\n   * This presents a fullscreen view of your video component on top of your app's UI. Note that even if `useNativeControls` is set to `false`,\n   * native controls will be visible in fullscreen mode.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the video once the fullscreen player has finished presenting,\n   * or rejects if there was an error, or if this was called on an Android device.\n   */\n  presentFullscreenPlayer = async (): Promise<AVPlaybackStatus> => {\n    return this._setFullscreen(true);\n  };\n\n  /**\n   * This dismisses the fullscreen video view.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the video once the fullscreen player has finished dismissing,\n   * or rejects if there was an error, or if this was called on an Android device.\n   */\n  dismissFullscreenPlayer = async (): Promise<AVPlaybackStatus> => {\n    return this._setFullscreen(false);\n  };\n\n  // ### Unified playback API ### (consistent with Audio.js)\n  // All calls automatically call onPlaybackStatusUpdate as a side effect.\n\n  /**\n   * @hidden\n   */\n  getStatusAsync = async (): Promise<AVPlaybackStatus> => {\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExponentAV.getStatusForVideo(tag)\n    );\n  };\n\n  /**\n   * @hidden\n   */\n  loadAsync = async (\n    source: AVPlaybackSource,\n    initialStatus: AVPlaybackStatusToSet = {},\n    downloadFirst: boolean = true\n  ): Promise<AVPlaybackStatus> => {\n    const { nativeSource, fullInitialStatus } =\n      await getNativeSourceAndFullInitialStatusForLoadAsync(source, initialStatus, downloadFirst);\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExponentAV.loadForVideo(tag, nativeSource, fullInitialStatus)\n    );\n  };\n\n  /**\n   * Equivalent to setting URI to `null`.\n   * @hidden\n   */\n  unloadAsync = async (): Promise<AVPlaybackStatus> => {\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExponentAV.unloadForVideo(tag)\n    );\n  };\n\n  componentWillUnmount() {\n    // Auto unload video to perform necessary cleanup safely\n    this.unloadAsync().catch(() => {\n      // Ignored rejection. Sometimes the unloadAsync code is executed when video is already unloaded.\n      // In such cases, it throws:\n      // \"[Unhandled promise rejection: Error: Invalid view returned from registry,\n      //  expecting EXVideo, got: (null)]\"\n    });\n  }\n\n  /**\n   * Set status API, only available while `isLoaded = true`.\n   * @hidden\n   */\n  setStatusAsync = async (status: AVPlaybackStatusToSet): Promise<AVPlaybackStatus> => {\n    assertStatusValuesInBounds(status);\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExponentAV.setStatusForVideo(tag, status)\n    );\n  };\n\n  /**\n   * @hidden\n   */\n  replayAsync = async (status: AVPlaybackStatusToSet = {}): Promise<AVPlaybackStatus> => {\n    if (status.positionMillis && status.positionMillis !== 0) {\n      throw new Error('Requested position after replay has to be 0.');\n    }\n\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExponentAV.replayVideo(tag, {\n        ...status,\n        positionMillis: 0,\n        shouldPlay: true,\n      })\n    );\n  };\n\n  /**\n   * Sets a function to be called regularly with the `AVPlaybackStatus` of the playback object.\n   *\n   * `onPlaybackStatusUpdate` will be called whenever a call to the API for this playback object completes\n   * (such as `setStatusAsync()`, `getStatusAsync()`, or `unloadAsync()`), nd will also be called at regular intervals\n   * while the media is in the loaded state.\n   *\n   * Set `progressUpdateIntervalMillis` via `setStatusAsync()` or `setProgressUpdateIntervalAsync()` to modify\n   * the interval with which `onPlaybackStatusUpdate` is called while loaded.\n   *\n   * @param onPlaybackStatusUpdate A function taking a single parameter `AVPlaybackStatus`.\n   */\n  setOnPlaybackStatusUpdate(onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null) {\n    this._onPlaybackStatusUpdate = onPlaybackStatusUpdate;\n    this.getStatusAsync();\n  }\n\n  // Methods of the Playback interface that are set via PlaybackMixin\n  playAsync!: () => Promise<AVPlaybackStatus>;\n  playFromPositionAsync!: (\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ) => Promise<AVPlaybackStatus>;\n  pauseAsync!: () => Promise<AVPlaybackStatus>;\n  stopAsync!: () => Promise<AVPlaybackStatus>;\n  setPositionAsync!: (\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ) => Promise<AVPlaybackStatus>;\n  setRateAsync!: (\n    rate: number,\n    shouldCorrectPitch: boolean,\n    pitchCorrectionQuality?: PitchCorrectionQuality\n  ) => Promise<AVPlaybackStatus>;\n  setVolumeAsync!: (volume: number, audioPan?: number) => Promise<AVPlaybackStatus>;\n  setIsMutedAsync!: (isMuted: boolean) => Promise<AVPlaybackStatus>;\n  setIsLoopingAsync!: (isLooping: boolean) => Promise<AVPlaybackStatus>;\n  setProgressUpdateIntervalAsync!: (\n    progressUpdateIntervalMillis: number\n  ) => Promise<AVPlaybackStatus>;\n\n  // Callback wrappers\n\n  _nativeOnPlaybackStatusUpdate = (event: { nativeEvent: AVPlaybackStatus }) => {\n    this._handleNewStatus(event.nativeEvent);\n  };\n\n  // TODO make sure we are passing the right stuff\n  _nativeOnLoadStart = () => {\n    if (this.props.onLoadStart) {\n      this.props.onLoadStart();\n    }\n  };\n\n  _nativeOnLoad = (event: { nativeEvent: AVPlaybackStatus }) => {\n    if (this.props.onLoad) {\n      this.props.onLoad(event.nativeEvent);\n    }\n    this._handleNewStatus(event.nativeEvent);\n  };\n\n  _nativeOnError = (event: { nativeEvent: { error: string } }) => {\n    const error: string = event.nativeEvent.error;\n    if (this.props.onError) {\n      this.props.onError(error);\n    }\n    this._handleNewStatus(getUnloadedStatus(error));\n  };\n\n  _nativeOnReadyForDisplay = (event: { nativeEvent: VideoReadyForDisplayEvent }) => {\n    if (this.props.onReadyForDisplay) {\n      this.props.onReadyForDisplay(event.nativeEvent);\n    }\n  };\n\n  _nativeOnFullscreenUpdate = (event: { nativeEvent: VideoFullscreenUpdateEvent }) => {\n    if (this.props.onFullscreenUpdate) {\n      this.props.onFullscreenUpdate(event.nativeEvent);\n    }\n  };\n\n  _renderPoster = () => {\n    const PosterComponent = this.props.PosterComponent ?? Image;\n\n    return this.props.usePoster && this.state.showPoster ? (\n      <PosterComponent\n        style={[_STYLES.poster, this.props.posterStyle]}\n        source={this.props.posterSource!}\n      />\n    ) : null;\n  };\n\n  render() {\n    maybeWarnAboutVideoDeprecation();\n\n    const source = getNativeSourceFromSource(this.props.source) || undefined;\n\n    let nativeResizeMode = ExpoVideoManagerConstants.ScaleNone;\n    if (this.props.resizeMode) {\n      const resizeMode = this.props.resizeMode;\n      if (resizeMode === ResizeMode.STRETCH) {\n        nativeResizeMode = ExpoVideoManagerConstants.ScaleToFill;\n      } else if (resizeMode === ResizeMode.CONTAIN) {\n        nativeResizeMode = ExpoVideoManagerConstants.ScaleAspectFit;\n      } else if (resizeMode === ResizeMode.COVER) {\n        nativeResizeMode = ExpoVideoManagerConstants.ScaleAspectFill;\n      }\n    }\n\n    // Set status via individual props\n    const status: AVPlaybackStatusToSet = { ...this.props.status };\n    (\n      [\n        'progressUpdateIntervalMillis',\n        'positionMillis',\n        'shouldPlay',\n        'rate',\n        'shouldCorrectPitch',\n        'volume',\n        'isMuted',\n        'isLooping',\n      ] as const\n    ).forEach((prop) => {\n      if (prop in this.props) {\n        status[prop] = this.props[prop] as any;\n      }\n    });\n\n    // Replace selected native props\n    const nativeProps: VideoNativeProps = {\n      ...omit(this.props, [\n        'source',\n        'onPlaybackStatusUpdate',\n        'usePoster',\n        'posterSource',\n        'posterStyle',\n        ...Object.keys(status),\n      ]),\n      style: [_STYLES.base, this.props.style],\n      videoStyle: [_STYLES.video, this.props.videoStyle],\n      source,\n      resizeMode: nativeResizeMode,\n      status,\n      onStatusUpdate: this._nativeOnPlaybackStatusUpdate,\n      onLoadStart: this._nativeOnLoadStart,\n      onLoad: this._nativeOnLoad,\n      onError: this._nativeOnError,\n      onReadyForDisplay: this._nativeOnReadyForDisplay,\n      onFullscreenUpdate: this._nativeOnFullscreenUpdate,\n    };\n\n    return (\n      <View style={nativeProps.style}>\n        <ExponentVideo ref={this._nativeRef} {...nativeProps} style={nativeProps.videoStyle} />\n        {this._renderPoster()}\n      </View>\n    );\n  }\n}\n\nfunction omit(props: Record<string, any>, propNames: string[]) {\n  const copied = { ...props };\n  for (const propName of propNames) {\n    delete copied[propName];\n  }\n  return copied;\n}\n\nfunction maybeWarnAboutVideoDeprecation() {\n  if (__DEV__ && !didWarnAboutVideoDeprecation) {\n    didWarnAboutVideoDeprecation = true;\n    console.log(\n      '⚠️ \\x1b[33m[expo-av]: Video component from `expo-av` is deprecated in favor of `expo-video`. ' +\n        'See the documentation at https://docs.expo.dev/versions/latest/sdk/video/ for the new API reference.'\n    );\n  }\n}\n\nObject.assign(Video.prototype, PlaybackMixin);\n\n// note(simek): TypeDoc cannot resolve correctly name of inline and default exported class\nexport default Video;\n"]}