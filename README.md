# 📱 DocuAI Analyzer

Um aplicativo móvel moderno para análise de documentos usando Inteligência Artificial. Desenvolvido com React Native, Expo e TypeScript.

## 🚀 Funcionalidades

- **🔐 Autenticação com Google** - Login seguro e rápido
- **📄 Upload de Documentos** - Suporte a PDF, DOC, DOCX, imagens e áudios
- **🤖 Análise com IA** - Integração com OpenAI para análise inteligente
- **📊 Relatórios Detalhados** - Resumos, pontos-chave e recomendações
- **💰 Sistema de Créditos** - Controle de uso e planos de assinatura
- **🌙 Modo Escuro** - Interface adaptável ao tema do sistema
- **📱 Multiplataforma** - iOS, Android e Web

## 🛠️ Tecnologias

- **React Native** + **Expo 53**
- **TypeScript** para tipagem estrita
- **Zustand** para gerenciamento de estado
- **Expo Router** para navegação
- **Google OAuth** para autenticação
- **OpenAI API** para análise de documentos
- **React Hook Form** para formulários

## ⚙️ Configuração

### 1. Instalar Dependências

```bash
npm install
```

### 2. Configurar Google OAuth

1. Acesse o [Google Cloud Console](https://console.cloud.google.com/)
2. Crie um novo projeto ou selecione um existente
3. Ative a **Google+ API**
4. Vá para **Credenciais** > **Criar credenciais** > **ID do cliente OAuth 2.0**
5. Selecione **Aplicativo da Web**
6. Configure as **origens autorizadas**:
   - `http://localhost:19006` (desenvolvimento)
7. Configure os **URIs de redirecionamento**:
   - `http://localhost:19006/auth` (desenvolvimento)
8. Copie o **Client ID**

### 3. Configurar OpenAI API

1. Acesse [OpenAI Platform](https://platform.openai.com/)
2. Faça login ou crie uma conta
3. Vá para **API Keys** no menu lateral
4. Clique em **Create new secret key**
5. Copie a chave da API
6. Configure billing em [Account Billing](https://platform.openai.com/account/billing)

### 4. Configurar Variáveis de Ambiente

1. Copie o arquivo de exemplo:
   ```bash
   cp .env.example .env
   ```

2. Edite o arquivo `.env` e adicione suas credenciais:
   ```env
   # Google OAuth
   EXPO_PUBLIC_GOOGLE_CLIENT_ID=seu_client_id_aqui.apps.googleusercontent.com
   
   # OpenAI API
   EXPO_PUBLIC_OPENAI_API_KEY=sk-sua_chave_openai_aqui
   EXPO_PUBLIC_OPENAI_MODEL=gpt-4o-mini
   EXPO_PUBLIC_OPENAI_MAX_TOKENS=2000
   ```

### 5. Iniciar o Aplicativo

```bash
npx expo start
```

## 📱 Como Usar

1. **Login**: Faça login com sua conta Google
2. **Upload**: Envie documentos, imagens ou áudios
3. **Análise**: Escolha o tipo de análise (Resumo, Contrato, Riscos)
4. **Resultados**: Visualize resumos, pontos-chave e recomendações
5. **Gerenciar**: Organize seus documentos e análises

## 🤖 Tipos de Análise

- **📄 Resumo Geral**: Análise completa do conteúdo
- **📋 Análise de Contrato**: Identificação de cláusulas e riscos
- **⚠️ Avaliação de Riscos**: Análise de potenciais problemas
- **🎯 Análise Personalizada**: Prompts customizados

## 🏗️ Estrutura do Projeto

```
├── app/                    # Roteamento e telas
│   ├── (tabs)/            # Navegação por abas
│   ├── auth/              # Telas de autenticação
│   └── _layout.tsx        # Layout principal
├── components/            # Componentes reutilizáveis
│   ├── ui/               # Componentes de interface
│   ├── ThemedText.tsx    # Texto com tema
│   └── ThemedView.tsx    # View com tema
├── store/                # Gerenciamento de estado
│   ├── authStore.ts      # Estado de autenticação
│   └── documentsStore.ts # Estado dos documentos
├── services/             # Serviços e APIs
│   ├── authService.ts    # Serviço de autenticação
│   └── openaiService.ts  # Serviço OpenAI
├── hooks/                # Hooks personalizados
├── constants/            # Constantes e temas
├── types/                # Tipos TypeScript
└── config/               # Configurações
```

## 💳 Sistema de Créditos

- **Créditos iniciais**: 10 créditos gratuitos
- **Custo por análise**: 1 crédito
- **Recarga**: Disponível no perfil do usuário

## 🎨 Design System

- **Cores**: Sistema de cores consistente com modo claro/escuro
- **Tipografia**: Hierarquia tipográfica bem definida
- **Espaçamento**: Sistema de espaçamento padronizado
- **Componentes**: Biblioteca de componentes reutilizáveis

## 🔧 Scripts Disponíveis

- `npm start` - Inicia o servidor de desenvolvimento
- `npm run android` - Executa no Android
- `npm run ios` - Executa no iOS
- `npm run web` - Executa na web
- `npm run lint` - Executa linting

## 📋 Próximos Passos

- [ ] Extração de texto de PDFs
- [ ] Sistema de pagamentos
- [ ] Notificações push
- [ ] Modo offline
- [ ] Compartilhamento de análises
- [ ] Exportação de relatórios

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT.

## 📞 Suporte

- 📧 Email: <EMAIL>
- 💬 Discord: [Comunidade DocuAI](https://discord.gg/docuai)
- 📖 Documentação: [docs.docuai.com](https://docs.docuai.com)
