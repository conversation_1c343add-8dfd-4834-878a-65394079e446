import { OPENAI_CONFIG } from '@/config/auth';
import * as FileSystem from 'expo-file-system';

export interface AnalysisRequest {
  documentId: string;
  content: string;
  type: 'document_summary' | 'contract_analysis' | 'risk_assessment' | 'custom';
  customPrompt?: string;
  documentType: 'text' | 'image' | 'audio';
}

export interface AnalysisResponse {
  success: boolean;
  analysis?: {
    summary: string;
    keyPoints: string[];
    risks?: string[];
    recommendations?: string[];
    confidence: number;
    extractedData: Record<string, any>;
  };
  error?: string;
  tokensUsed?: number;
}

export interface TranscriptionResponse {
  success: boolean;
  text?: string;
  error?: string;
}

class OpenAIService {
  private apiKey: string;
  private baseUrl: string = 'https://api.openai.com/v1';

  constructor() {
    this.apiKey = OPENAI_CONFIG.API_KEY;
    
    if (!this.apiKey || this.apiKey === 'YOUR_OPENAI_API_KEY_HERE') {
      console.warn('OpenAI API Key não configurada. Configure em .env');
    }
  }

  /**
   * Analisa texto usando GPT
   */
  async analyzeText(request: AnalysisRequest): Promise<AnalysisResponse> {
    try {
      if (!this.apiKey || this.apiKey === 'YOUR_OPENAI_API_KEY_HERE') {
        return {
          success: false,
          error: 'Chave da API OpenAI não configurada'
        };
      }

      const prompt = this.buildPrompt(request);
      
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: OPENAI_CONFIG.MODEL,
          messages: [
            {
              role: 'system',
              content: 'Você é um assistente especializado em análise de documentos. Forneça análises precisas, detalhadas e úteis em português brasileiro.'
            },
            {
              role: 'user',
              content: `${prompt}\n\nDocumento para análise:\n${request.content}`
            }
          ],
          max_tokens: OPENAI_CONFIG.MAX_TOKENS,
          temperature: OPENAI_CONFIG.TEMPERATURE,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: `Erro da API OpenAI: ${errorData.error?.message || 'Erro desconhecido'}`
        };
      }

      const data = await response.json();
      const analysisText = data.choices[0]?.message?.content;

      if (!analysisText) {
        return {
          success: false,
          error: 'Resposta vazia da OpenAI'
        };
      }

      // Processar a resposta da IA para extrair informações estruturadas
      const analysis = this.parseAnalysisResponse(analysisText);

      return {
        success: true,
        analysis,
        tokensUsed: data.usage?.total_tokens || 0
      };

    } catch (error) {
      console.error('Erro na análise de texto:', error);
      return {
        success: false,
        error: 'Erro interno na análise'
      };
    }
  }

  /**
   * Analisa imagem usando GPT-4 Vision
   */
  async analyzeImage(request: AnalysisRequest, imageUri: string): Promise<AnalysisResponse> {
    try {
      if (!this.apiKey || this.apiKey === 'YOUR_OPENAI_API_KEY_HERE') {
        return {
          success: false,
          error: 'Chave da API OpenAI não configurada'
        };
      }

      // Converter imagem para base64
      const base64Image = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      const prompt = this.buildPrompt(request);

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini', // Modelo com suporte a visão
          messages: [
            {
              role: 'system',
              content: 'Você é um assistente especializado em análise de documentos e imagens. Analise o conteúdo visual e extraia informações relevantes em português brasileiro.'
            },
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: `data:image/jpeg;base64,${base64Image}`
                  }
                }
              ]
            }
          ],
          max_tokens: OPENAI_CONFIG.MAX_TOKENS,
          temperature: OPENAI_CONFIG.TEMPERATURE,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: `Erro da API OpenAI: ${errorData.error?.message || 'Erro desconhecido'}`
        };
      }

      const data = await response.json();
      const analysisText = data.choices[0]?.message?.content;

      if (!analysisText) {
        return {
          success: false,
          error: 'Resposta vazia da OpenAI'
        };
      }

      const analysis = this.parseAnalysisResponse(analysisText);

      return {
        success: true,
        analysis,
        tokensUsed: data.usage?.total_tokens || 0
      };

    } catch (error) {
      console.error('Erro na análise de imagem:', error);
      return {
        success: false,
        error: 'Erro interno na análise de imagem'
      };
    }
  }

  /**
   * Transcreve áudio usando Whisper
   */
  async transcribeAudio(audioUri: string): Promise<TranscriptionResponse> {
    try {
      if (!this.apiKey || this.apiKey === 'YOUR_OPENAI_API_KEY_HERE') {
        return {
          success: false,
          error: 'Chave da API OpenAI não configurada'
        };
      }

      const formData = new FormData();
      formData.append('file', {
        uri: audioUri,
        type: 'audio/mp3',
        name: 'audio.mp3',
      } as any);
      formData.append('model', 'whisper-1');
      formData.append('language', 'pt');

      const response = await fetch(`${this.baseUrl}/audio/transcriptions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'multipart/form-data',
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: `Erro na transcrição: ${errorData.error?.message || 'Erro desconhecido'}`
        };
      }

      const data = await response.json();

      return {
        success: true,
        text: data.text
      };

    } catch (error) {
      console.error('Erro na transcrição de áudio:', error);
      return {
        success: false,
        error: 'Erro interno na transcrição'
      };
    }
  }

  /**
   * Constrói o prompt baseado no tipo de análise
   */
  private buildPrompt(request: AnalysisRequest): string {
    if (request.type === 'custom' && request.customPrompt) {
      return request.customPrompt;
    }
    
    return OPENAI_CONFIG.PROMPTS[request.type];
  }

  /**
   * Processa a resposta da IA para extrair informações estruturadas
   */
  private parseAnalysisResponse(text: string) {
    // Extrair pontos-chave (linhas que começam com números ou bullets)
    const keyPointsRegex = /(?:^\d+\.|^[-•*])\s*(.+)$/gm;
    const keyPoints: string[] = [];
    let match;
    
    while ((match = keyPointsRegex.exec(text)) !== null) {
      keyPoints.push(match[1].trim());
    }

    // Extrair riscos (procurar por seções de risco)
    const riskRegex = /(?:risco|risk|atenção|cuidado|problema).*?:?\s*(.+?)(?=\n|$)/gi;
    const risks: string[] = [];
    
    while ((match = riskRegex.exec(text)) !== null) {
      risks.push(match[1].trim());
    }

    // Extrair recomendações
    const recommendationRegex = /(?:recomend|suger|deve|deveria).*?:?\s*(.+?)(?=\n|$)/gi;
    const recommendations: string[] = [];
    
    while ((match = recommendationRegex.exec(text)) !== null) {
      recommendations.push(match[1].trim());
    }

    // Calcular confiança baseada no tamanho e estrutura da resposta
    const confidence = Math.min(0.95, Math.max(0.6, text.length / 1000));

    return {
      summary: text.split('\n')[0] || text.substring(0, 200) + '...',
      keyPoints: keyPoints.length > 0 ? keyPoints : [text.substring(0, 100) + '...'],
      risks: risks.length > 0 ? risks : undefined,
      recommendations: recommendations.length > 0 ? recommendations : undefined,
      confidence,
      extractedData: {
        fullText: text,
        wordCount: text.split(' ').length,
        analyzedAt: new Date().toISOString()
      }
    };
  }
}

export const openaiService = new OpenAIService();
