# expo-application

Gets native application information such as its ID, app name, and build version at runtime.

# API documentation

- [Documentation for the latest stable release](https://docs.expo.dev/versions/latest/sdk/application/)
- [Documentation for the main branch](https://docs.expo.dev/versions/unversioned/sdk/application/)

# Installation in managed Expo projects

For [managed](https://docs.expo.dev/archive/managed-vs-bare/) Expo projects, please follow the installation instructions in the [API documentation for the latest stable release](#https://docs.expo.dev/versions/latest/sdk/application/).

# Installation in bare React Native projects

For bare React Native projects, you must ensure that you have [installed and configured the `expo` package](https://docs.expo.dev/bare/installing-expo-modules/) before continuing.

### Add the package to your npm dependencies

```
npx expo install expo-application
```

# Contributing

Contributions are very welcome! Please refer to guidelines described in the [contributing guide](https://github.com/expo/expo#contributing).
