import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useDocumentsStore } from '@/store/documentsStore';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Theme } from '@/constants/Colors';
import { router } from 'expo-router';

export default function AnalysisScreen() {
  const { analyses } = useDocumentsStore();
  const [refreshing, setRefreshing] = useState(false);
  
  const primaryColor = useThemeColor({}, 'primary');
  const successColor = useThemeColor({}, 'success');
  const borderColor = useThemeColor({}, 'border');

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // TODO: Implementar refresh das análises
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return successColor;
    if (confidence >= 0.6) return '#F59E0B';
    return '#EF4444';
  };

  const AnalysisCard = ({ analysis }: { analysis: any }) => (
    <TouchableOpacity
      style={styles.analysisCard}
      onPress={() => router.push(`/analysis/${analysis.id}`)}
      activeOpacity={0.7}
    >
      <ThemedView style={[styles.cardContent, { borderColor }]}>
        <View style={styles.cardHeader}>
          <View style={styles.analysisInfo}>
            <Ionicons name="analytics" size={24} color={primaryColor} />
            <View style={styles.analysisDetails}>
              <ThemedText type="subtitle" style={styles.analysisTitle}>
                Análise #{analysis.id.slice(-6)}
              </ThemedText>
              <ThemedText type="caption" style={styles.analysisDate}>
                {formatDate(analysis.analyzedAt)}
              </ThemedText>
            </View>
          </View>
          
          <View style={styles.confidenceContainer}>
            <View style={[
              styles.confidenceBadge, 
              { backgroundColor: getConfidenceColor(analysis.confidence) + '20' }
            ]}>
              <ThemedText 
                type="caption" 
                style={[styles.confidenceText, { color: getConfidenceColor(analysis.confidence) }]}
              >
                {Math.round(analysis.confidence * 100)}%
              </ThemedText>
            </View>
          </View>
        </View>

        <View style={styles.summaryContainer}>
          <ThemedText type="caption" weight="medium" style={styles.summaryLabel}>
            Resumo:
          </ThemedText>
          <ThemedText style={styles.summaryText} numberOfLines={3}>
            {analysis.summary}
          </ThemedText>
        </View>

        {analysis.keyPoints && analysis.keyPoints.length > 0 && (
          <View style={styles.keyPointsContainer}>
            <ThemedText type="caption" weight="medium" style={styles.keyPointsLabel}>
              Pontos-chave:
            </ThemedText>
            {analysis.keyPoints.slice(0, 2).map((point: string, index: number) => (
              <View key={index} style={styles.keyPoint}>
                <Ionicons name="checkmark-circle" size={16} color={successColor} />
                <ThemedText type="caption" style={styles.keyPointText}>
                  {point}
                </ThemedText>
              </View>
            ))}
            {analysis.keyPoints.length > 2 && (
              <ThemedText type="caption" style={styles.morePoints}>
                +{analysis.keyPoints.length - 2} pontos adicionais
              </ThemedText>
            )}
          </View>
        )}

        <View style={styles.cardFooter}>
          <Ionicons name="chevron-forward" size={20} color={borderColor} />
        </View>
      </ThemedView>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="analytics-outline" size={64} color="#9CA3AF" />
      <ThemedText type="subtitle" style={styles.emptyTitle}>
        Nenhuma análise ainda
      </ThemedText>
      <ThemedText type="caption" style={styles.emptyDescription}>
        Faça upload de um documento e solicite uma análise para ver os resultados aqui
      </ThemedText>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.header}>
        <View>
          <ThemedText type="title">Análises</ThemedText>
          <ThemedText type="caption" style={styles.subtitle}>
            {analyses.length} análise{analyses.length !== 1 ? 's' : ''} realizadas
          </ThemedText>
        </View>
      </ThemedView>

      <FlatList
        data={analyses}
        renderItem={({ item }) => <AnalysisCard analysis={item} />}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  subtitle: {
    opacity: 0.7,
    marginTop: 2,
  },
  listContent: {
    padding: Theme.spacing.lg,
    flexGrow: 1,
  },
  analysisCard: {
    marginBottom: Theme.spacing.md,
  },
  cardContent: {
    padding: Theme.spacing.lg,
    borderRadius: Theme.borderRadius.lg,
    borderWidth: 1,
    ...Theme.shadows.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Theme.spacing.md,
  },
  analysisInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  analysisDetails: {
    marginLeft: Theme.spacing.sm,
    flex: 1,
  },
  analysisTitle: {
    marginBottom: 2,
  },
  analysisDate: {
    opacity: 0.7,
  },
  confidenceContainer: {
    marginLeft: Theme.spacing.sm,
  },
  confidenceBadge: {
    paddingHorizontal: Theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: Theme.borderRadius.sm,
  },
  confidenceText: {
    fontSize: 12,
    fontWeight: '600',
  },
  summaryContainer: {
    marginBottom: Theme.spacing.md,
  },
  summaryLabel: {
    marginBottom: Theme.spacing.xs,
    opacity: 0.8,
  },
  summaryText: {
    lineHeight: 20,
  },
  keyPointsContainer: {
    marginBottom: Theme.spacing.md,
  },
  keyPointsLabel: {
    marginBottom: Theme.spacing.xs,
    opacity: 0.8,
  },
  keyPoint: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Theme.spacing.xs,
  },
  keyPointText: {
    marginLeft: Theme.spacing.xs,
    flex: 1,
    lineHeight: 18,
  },
  morePoints: {
    marginTop: Theme.spacing.xs,
    opacity: 0.6,
    fontStyle: 'italic',
  },
  cardFooter: {
    alignItems: 'flex-end',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.xl,
  },
  emptyTitle: {
    marginTop: Theme.spacing.lg,
    marginBottom: Theme.spacing.sm,
    textAlign: 'center',
  },
  emptyDescription: {
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
});
