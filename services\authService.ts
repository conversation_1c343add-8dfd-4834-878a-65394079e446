import { AUTH_CONFIG } from '@/config/auth';
import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';

// Configurar o WebBrowser para autenticação
WebBrowser.maybeCompleteAuthSession();

export interface GoogleUser {
  id: string;
  email: string;
  name: string;
  picture?: string;
  given_name?: string;
  family_name?: string;
}

export interface AuthResult {
  success: boolean;
  user?: GoogleUser;
  accessToken?: string;
  error?: string;
}

class AuthService {
  private clientId: string;
  private redirectUri: string;

  constructor() {
    // Obter o Client ID do Google da configuração
    this.clientId = AUTH_CONFIG.GOOGLE_CLIENT_ID;
    this.redirectUri = AuthSession.makeRedirectUri({
      scheme: 'docuai',
      path: 'auth',
    });

    if (!this.clientId || this.clientId === 'YOUR_GOOGLE_CLIENT_ID_HERE') {
      console.warn('Google Client ID não configurado. Configure em config/auth.ts');
    }
  }

  /**
   * Inicia o fluxo de autenticação com Google
   */
  async signInWithGoogle(): Promise<AuthResult> {
    try {
      // Configurar a requisição de autenticação (usando implicit flow para simplicidade)
      const request = new AuthSession.AuthRequest({
        clientId: this.clientId,
        scopes: AUTH_CONFIG.SCOPES,
        responseType: AuthSession.ResponseType.Token,
        redirectUri: this.redirectUri,
      });

      // Fazer a requisição de autenticação
      const result = await request.promptAsync({
        authorizationEndpoint: AUTH_CONFIG.ENDPOINTS.authorization,
      });

      if (result.type === 'success' && result.params.access_token) {
        // Obter informações do usuário
        const userInfo = await this.getUserInfo(result.params.access_token);

        return {
          success: true,
          user: userInfo,
          accessToken: result.params.access_token,
        };
      }

      return {
        success: false,
        error: result.type === 'cancel' ? 'Autenticação cancelada' : 'Falha na autenticação',
      };
    } catch (error) {
      console.error('Erro na autenticação com Google:', error);
      return {
        success: false,
        error: 'Erro interno na autenticação',
      };
    }
  }



  /**
   * Obtém informações do usuário usando o token de acesso
   */
  private async getUserInfo(accessToken: string): Promise<GoogleUser> {
    const response = await fetch(
      'https://www.googleapis.com/oauth2/v2/userinfo',
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    const userInfo = await response.json();

    return {
      id: userInfo.id,
      email: userInfo.email,
      name: userInfo.name,
      picture: userInfo.picture,
      given_name: userInfo.given_name,
      family_name: userInfo.family_name,
    };
  }

  /**
   * Faz logout (limpa tokens locais)
   */
  async signOut(): Promise<void> {
    // Em uma implementação real, você pode revogar o token
    // Por enquanto, apenas limpamos os dados locais
    return Promise.resolve();
  }

  /**
   * Verifica se o usuário está autenticado
   */
  async isAuthenticated(): Promise<boolean> {
    // Implementar verificação de token válido
    // Por enquanto, retorna false
    return false;
  }
}

export const authService = new AuthService();
